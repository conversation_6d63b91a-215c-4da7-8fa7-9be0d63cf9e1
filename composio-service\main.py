"""
Uru Composio Bridge Service

This service provides a fully white-labeled interface to Composio's MCP tools.
All Composio branding, URLs, and implementation details are hidden from end users.
Only Uru-branded endpoints and responses are exposed.
"""

import os
import json
import secrets
import logging
import urllib.parse
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

import httpx
from fastapi import FastAPI, HTTPException, Depends, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from fastapi.responses import RedirectResponse
from pydantic import BaseModel, Field
from supabase import create_client, Client
from cryptography.fernet import Fernet
from integration_config import integration_registry, IntegrationConfig, AuthType

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
COMPOSIO_API_KEY = os.getenv("URU_COMPOSIO_API_KEY")
COMPOSIO_BASE_URL = os.getenv("URU_COMPOSIO_BASE_URL", "https://backend.composio.dev/api")
JWT_SECRET = os.getenv("JWT_SECRET")
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY")
# Parse CORS origins with fallback
cors_origins_env = os.getenv("CORS_ORIGINS", "")
if cors_origins_env:
    try:
        # Try JSON parsing first
        import json
        CORS_ORIGINS = json.loads(cors_origins_env)
    except:
        # Fall back to comma-separated parsing
        CORS_ORIGINS = [origin.strip() for origin in cors_origins_env.split(",") if origin.strip()]
else:
    # Default development origins for new architecture
    CORS_ORIGINS = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8001",
        "http://localhost:8002",
        "http://localhost:8003"
    ]

# Google OAuth configuration
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")
FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:3000")
AUTH_SERVICE_URL = os.getenv("AUTH_SERVICE_URL", "http://localhost:8003")
INTEGRATIONS_SERVICE_URL = os.getenv("INTEGRATIONS_SERVICE_URL", "http://localhost:8002")
COMPOSIO_SERVICE_URL = os.getenv("COMPOSIO_SERVICE_URL", "http://localhost:8001")

# Google OAuth scopes for different apps
GOOGLE_SCOPES = {
    "gmail": [
        "https://www.googleapis.com/auth/gmail.modify",
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile"
    ],
    "drive": [
        "https://www.googleapis.com/auth/drive",
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile"
    ],
    "calendar": [
        "https://www.googleapis.com/auth/calendar",
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile"
    ]
}

# Initialize encryption (will be properly initialized in lifespan)
cipher_suite = None

# Initialize Supabase (will be properly initialized in lifespan)
supabase: Client = None

# HTTP client for Composio API calls (will be initialized in lifespan)
http_client = None

# Security
security = HTTPBearer()

# Pydantic models
class ComposioEntityRequest(BaseModel):
    """Request to create or retrieve Composio entity"""
    pass

class ComposioConnectRequest(BaseModel):
    """Request to connect an app via Composio"""
    redirect_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = {}

class ComposioExecuteRequest(BaseModel):
    """Request to execute MCP tool via Composio"""
    tool_name: str
    parameters: Dict[str, Any] = {}

class ComposioWebhookRequest(BaseModel):
    """Webhook payload from Composio"""
    event_type: str
    entity_id: str
    connection_id: Optional[str] = None
    app_name: Optional[str] = None
    data: Dict[str, Any] = {}

# Startup/shutdown handlers
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan handler"""
    logger.info("🚀 Uru Composio Bridge Service starting up...")

    # Debug environment variables
    logger.info(f"GOOGLE_CLIENT_ID: {GOOGLE_CLIENT_ID}")
    logger.info(f"GOOGLE_CLIENT_SECRET: {'***' if GOOGLE_CLIENT_SECRET else 'None'}")
    logger.info(f"FRONTEND_URL: {FRONTEND_URL}")
    logger.info(f"AUTH_SERVICE_URL: {AUTH_SERVICE_URL}")
    logger.info(f"INTEGRATIONS_SERVICE_URL: {INTEGRATIONS_SERVICE_URL}")

    # Validate required environment variables
    critical_vars = [
        "SUPABASE_URL", "SUPABASE_KEY", "JWT_SECRET", "ENCRYPTION_KEY"
    ]
    optional_vars = ["URU_COMPOSIO_API_KEY"]

    missing_critical = [var for var in critical_vars if not os.getenv(var)]
    missing_optional = [var for var in optional_vars if not os.getenv(var)]

    if missing_critical:
        logger.error(f"❌ Missing critical environment variables: {missing_critical}")
        logger.error("💡 Please ensure these environment variables are set in your Elestio deployment:")
        for var in missing_critical:
            logger.error(f"   - {var}")
        logger.error("🔧 Check your Elestio environment variables configuration")
        raise RuntimeError(f"Missing critical environment variables: {missing_critical}")

    if missing_optional:
        logger.warning(f"⚠️ Missing optional environment variables: {missing_optional}")
        logger.warning("🔧 Composio integration features will be limited without these variables")
        logger.warning("💡 Add URU_COMPOSIO_API_KEY to enable full Composio functionality")

    # Initialize encryption
    global cipher_suite
    try:
        if ENCRYPTION_KEY:
            cipher_suite = Fernet(ENCRYPTION_KEY.encode())
            logger.info("✅ Encryption initialized with provided key")
        else:
            # This should not happen due to validation above, but handle gracefully
            cipher_suite = Fernet(Fernet.generate_key())
            logger.warning("⚠️ Generated new encryption key - tokens will not persist across restarts")
    except Exception as e:
        logger.error(f"❌ Failed to initialize encryption: {e}")
        raise RuntimeError(f"Encryption initialization failed: {e}")

    # Initialize Supabase
    global supabase
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        logger.info("✅ Supabase client initialized")
    except Exception as e:
        logger.error(f"❌ Failed to initialize Supabase client: {e}")
        raise RuntimeError(f"Supabase initialization failed: {e}")

    # Initialize HTTP client
    global http_client
    http_client = httpx.AsyncClient(timeout=30.0)

    # Test Composio API connectivity (only if API key is available)
    if COMPOSIO_API_KEY:
        try:
            response = await http_client.get(
                f"{COMPOSIO_BASE_URL}/v1/entities",
                headers={"X-API-Key": COMPOSIO_API_KEY}
            )
            if response.status_code == 200:
                logger.info("✅ Composio API connectivity verified")
            else:
                logger.warning(f"⚠️ Composio API returned status {response.status_code}")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Composio API: {e}")
    else:
        logger.warning("⚠️ Skipping Composio API test - no API key provided")

    # Test Supabase connectivity
    try:
        result = supabase.table('employees').select('id').limit(1).execute()
        logger.info("✅ Supabase connectivity verified")
    except Exception as e:
        logger.error(f"❌ Failed to connect to Supabase: {e}")

    logger.info("🎉 Uru Composio Bridge Service ready!")

    yield

    # Cleanup
    if http_client:
        await http_client.aclose()
    logger.info("👋 Uru Composio Bridge Service shutting down...")

# Initialize FastAPI app
app = FastAPI(
    title="Uru Composio Bridge Service",
    description="White-labeled MCP tool integration via Composio",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS if CORS_ORIGINS else ["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Authentication dependency
async def get_current_employee(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Validate JWT token and return employee data"""
    try:
        import jwt
        
        # Decode JWT
        payload = jwt.decode(credentials.credentials, JWT_SECRET, algorithms=["HS256"])
        employee_id = payload.get("employee_id")
        
        if not employee_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        
        # Get employee data with workspace
        result = supabase.table('employees').select(
            "*, workspaces!inner(id, slug, name)"
        ).eq("id", employee_id).single().execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Employee not found"
            )
        
        return result.data
        
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )

# Utility functions
def encrypt_data(data: str) -> str:
    """Encrypt sensitive data"""
    return cipher_suite.encrypt(data.encode()).decode()

def decrypt_data(encrypted_data: str) -> str:
    """Decrypt sensitive data"""
    return cipher_suite.decrypt(encrypted_data.encode()).decode()

async def get_or_create_composio_entity(employee_id: str, employee_email: str) -> str:
    """Get or create Composio entity for employee"""
    try:
        # Check if employee already has a Composio entity
        result = supabase.table('employees').select('composio_entity_id').eq('id', employee_id).single().execute()

        if result.data and result.data.get('composio_entity_id'):
            return result.data['composio_entity_id']

        # Create virtual Composio entity (Composio doesn't use explicit entities)
        # We create a virtual entity ID for tracking purposes
        entity_id = f"uru_employee_{employee_id}"

        # Update employee record with entity ID
        supabase.table('employees').update({
            'composio_entity_id': entity_id,
            'composio_entity_created_at': datetime.now(timezone.utc).isoformat()
        }).eq('id', employee_id).execute()

        logger.info(f"Created Composio entity {entity_id} for employee {employee_id}")
        return entity_id

    except Exception as e:
        logger.error(f"Error managing Composio entity: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Entity management failed"
        )

async def refresh_oauth_token(employee_id: str, app_name: str, connection: Dict[str, Any]) -> None:
    """Refresh OAuth token for a connection"""
    try:
        if not connection.get("refresh_token"):
            logger.warning(f"No refresh token available for {app_name} connection")
            return

        refresh_token = decrypt_data(connection["refresh_token"])

        token_data = {
            "client_id": GOOGLE_CLIENT_ID,
            "client_secret": GOOGLE_CLIENT_SECRET,
            "refresh_token": refresh_token,
            "grant_type": "refresh_token"
        }

        response = await http_client.post(
            "https://oauth2.googleapis.com/token",
            data=token_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        if response.status_code != 200:
            logger.error(f"Token refresh failed for {app_name}: {response.text}")
            # Mark connection as expired
            supabase.table('composio_connections').update({
                "status": "expired",
                "updated_at": datetime.now(timezone.utc).isoformat()
            }).eq("employee_id", employee_id).eq("app_name", app_name).execute()
            return

        tokens = response.json()
        access_token = tokens.get("access_token")
        expires_in = tokens.get("expires_in", 3600)
        new_refresh_token = tokens.get("refresh_token", refresh_token)

        # Update tokens in oauth_tokens table
        provider_name = f"composio_{app_name}"
        supabase.table('oauth_tokens').update({
            "access_token": encrypt_data(access_token),
            "refresh_token": encrypt_data(new_refresh_token),
            "expires_at": (datetime.now(timezone.utc) + timedelta(seconds=expires_in)).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat()
        }).eq("employee_id", employee_id).eq("provider", provider_name).execute()

        logger.info(f"Successfully refreshed token for {app_name}")

    except Exception as e:
        logger.error(f"Error refreshing token for {app_name}: {e}")

async def get_or_create_auth_config(app_name: str) -> str:
    """Get or create Composio auth config (integration) for an app"""
    try:
        # Check if we already have an auth config for this app
        # We'll store auth config IDs in a simple cache or database
        # For now, let's create one each time (Composio allows multiple configs)

        auth_config_data = {
            "name": f"Uru {app_name.title()} Integration",
            "appName": app_name,
            "authMode": "OAUTH2",
            "useComposioAuth": True  # Use Composio's managed OAuth for simplicity
        }

        response = await http_client.post(
            f"{COMPOSIO_BASE_URL}/v1/integrations",
            headers={"X-API-Key": COMPOSIO_API_KEY, "Content-Type": "application/json"},
            json=auth_config_data
        )

        if response.status_code not in [200, 201]:
            logger.error(f"Failed to create auth config for {app_name}: {response.text}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create auth config for {app_name}"
            )

        result = response.json()
        auth_config_id = result.get("id")

        logger.info(f"Created auth config for {app_name}: {auth_config_id}")
        return auth_config_id

    except Exception as e:
        logger.error(f"Error creating auth config for {app_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create auth config for {app_name}"
        )

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "uru-composio-bridge",
        "version": "1.0.0",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "composio_api": "connected" if COMPOSIO_API_KEY else "not_configured",
        "database": "connected"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with service information"""
    return {
        "service": "Uru Composio Bridge",
        "description": "White-labeled MCP tool integration",
        "version": "1.0.0",
        "endpoints": {
            "health": "GET /health",
            "entities": "POST /api/uru/composio/entities",
            "connect": "POST /api/uru/composio/connect/{app_name}",
            "execute": "POST /api/uru/composio/execute",
            "webhook": "POST /api/uru/composio/webhook"
        }
    }

# ===========================================
# COMPOSIO BRIDGE API ENDPOINTS
# ===========================================

@app.post("/api/uru/composio/entities")
async def create_or_get_entity(
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Create or retrieve Composio entity for employee (white-labeled)"""
    try:
        entity_id = await get_or_create_composio_entity(
            employee["id"],
            employee["email"]
        )

        return {
            "success": True,
            "entity_id": entity_id,
            "message": "Entity ready for app connections"
        }

    except Exception as e:
        logger.error(f"Entity creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to prepare workspace for app connections"
        )

@app.post("/api/uru/composio/connect/{app_name}")
async def connect_app(
    app_name: str,
    request: ComposioConnectRequest,
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Initiate app connection via our own OAuth flow (fully white-labeled)"""
    try:
        # Validate app name
        if app_name.lower() not in GOOGLE_SCOPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported app: {app_name}"
            )

        # Check if already connected using oauth_tokens table
        provider_name = f"composio_{app_name.lower()}"
        existing_token = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).eq("provider", provider_name).execute()

        if existing_token.data:
            return {
                "success": True,
                "app_name": app_name,
                "already_connected": True,
                "message": f"Your {app_name.title()} account is already connected"
            }

        # Generate OAuth state for security
        oauth_state = secrets.token_urlsafe(32)

        # Store OAuth state in database
        oauth_record = {
            "employee_id": employee["id"],
            "provider": app_name.lower(),
            "state_token": oauth_state,
            "redirect_url": request.redirect_url or f"{FRONTEND_URL}/app/settings",
            "requested_scopes": GOOGLE_SCOPES[app_name.lower()],
            "expires_at": (datetime.now(timezone.utc) + timedelta(minutes=10)).isoformat()
        }

        supabase.table('oauth_states').upsert(
            oauth_record,
            on_conflict="employee_id,provider"
        ).execute()

        # Build Google OAuth URL
        scopes = " ".join(GOOGLE_SCOPES[app_name.lower()])
        oauth_params = {
            "client_id": GOOGLE_CLIENT_ID,
            "redirect_uri": f"{COMPOSIO_SERVICE_URL}/api/uru/composio/oauth/callback/{app_name}",
            "scope": scopes,
            "response_type": "code",
            "state": oauth_state,
            "access_type": "offline",
            "prompt": "consent"
        }

        authorization_url = "https://accounts.google.com/o/oauth2/v2/auth?" + urllib.parse.urlencode(oauth_params)

        logger.info(f"Generated OAuth URL for {app_name} for employee {employee['id']}")

        return {
            "success": True,
            "app_name": app_name,
            "authorization_url": authorization_url,
            "state": oauth_state,
            "message": f"Visit the authorization URL to connect your {app_name.title()} account"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"App connection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to connect app"
        )

@app.get("/api/uru/composio/oauth/callback/{app_name}")
async def oauth_callback(
    app_name: str,
    code: str,
    state: str,
    error: Optional[str] = None
):
    """Handle OAuth callback from Google (fully white-labeled)"""
    try:
        if error:
            logger.error(f"OAuth error for {app_name}: {error}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=oauth_denied&app={app_name}",
                status_code=302
            )

        # Verify OAuth state
        oauth_state_result = supabase.table('oauth_states').select("*").eq(
            "state_token", state
        ).eq("provider", app_name.lower()).single().execute()

        if not oauth_state_result.data:
            logger.error(f"Invalid OAuth state: {state}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=invalid_state&app={app_name}",
                status_code=302
            )

        oauth_state_data = oauth_state_result.data
        employee_id = oauth_state_data["employee_id"]
        redirect_url = oauth_state_data["redirect_url"]

        # Exchange code for tokens
        token_data = {
            "client_id": GOOGLE_CLIENT_ID,
            "client_secret": GOOGLE_CLIENT_SECRET,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": f"{COMPOSIO_SERVICE_URL}/api/uru/composio/oauth/callback/{app_name}"
        }

        token_response = await http_client.post(
            "https://oauth2.googleapis.com/token",
            data=token_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        if token_response.status_code != 200:
            logger.error(f"Token exchange failed: {token_response.text}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=token_exchange_failed&app={app_name}",
                status_code=302
            )

        tokens = token_response.json()
        access_token = tokens.get("access_token")
        refresh_token = tokens.get("refresh_token")
        expires_in = tokens.get("expires_in", 3600)

        if not access_token:
            logger.error("No access token received")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=no_access_token&app={app_name}",
                status_code=302
            )

        # Get user info from Google
        user_info_response = await http_client.get(
            "https://www.googleapis.com/oauth2/v2/userinfo",
            headers={"Authorization": f"Bearer {access_token}"}
        )

        user_info = {}
        if user_info_response.status_code == 200:
            user_info = user_info_response.json()

        # Store tokens in oauth_tokens table (standardized approach)
        provider_name = f"composio_{app_name.lower()}"
        oauth_record = {
            "employee_id": employee_id,
            "provider": provider_name,
            "access_token": encrypt_data(access_token),
            "refresh_token": encrypt_data(refresh_token) if refresh_token else None,
            "expires_at": (datetime.now(timezone.utc) + timedelta(seconds=expires_in)).isoformat(),
            "scopes": GOOGLE_SCOPES[app_name.lower()],
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "metadata": {
                "app_name": app_name.lower(),
                "connected_via": "uru_oauth",
                "google_user_id": user_info.get("id"),
                "google_email": user_info.get("email"),
                "user_info": user_info,
                "connected_at": datetime.now(timezone.utc).isoformat()
            }
        }

        # Upsert oauth token record
        supabase.table('oauth_tokens').upsert(
            oauth_record,
            on_conflict="employee_id,provider"
        ).execute()

        # Clean up OAuth state
        supabase.table('oauth_states').delete().eq("state_token", state).execute()

        logger.info(f"Successfully connected {app_name} for employee {employee_id}")

        # Redirect back to frontend with success
        return RedirectResponse(
            url=f"{redirect_url}?success=true&app={app_name}&connected=true",
            status_code=302
        )

    except Exception as e:
        logger.error(f"OAuth callback error: {e}")
        return RedirectResponse(
            url=f"{FRONTEND_URL}/app/settings?error=callback_failed&app={app_name}",
            status_code=302
        )

@app.post("/api/uru/composio/execute")
async def execute_tool(
    request: ComposioExecuteRequest,
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Execute MCP tool using our stored OAuth tokens (fully white-labeled)"""
    try:
        # Determine which app this tool belongs to
        tool_app_mapping = {
            "send_email": "gmail",
            "list_emails": "gmail",
            "read_email": "gmail",
            "gdrive_upload": "drive",
            "gdrive_download": "drive",
            "gdrive_list": "drive",
            "list_events": "calendar",
            "create_event": "calendar",
            "update_event": "calendar"
        }

        required_app = tool_app_mapping.get(request.tool_name)
        if not required_app:
            # For tools that don't require specific app auth, use Composio directly
            entity_id = employee.get("composio_entity_id")
            if not entity_id:
                entity_id = await get_or_create_composio_entity(employee["id"], employee["email"])

            execution_data = {
                "user_id": entity_id,
                "tool_name": request.tool_name,
                "parameters": request.parameters
            }

            response = await http_client.post(
                f"{COMPOSIO_BASE_URL}/v3/tools/execute",
                headers={"x-api-key": COMPOSIO_API_KEY, "Content-Type": "application/json"},
                json=execution_data
            )

            if response.status_code != 200:
                logger.error(f"Tool execution failed: {response.text}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Tool execution failed"
                )

            execution_result = response.json()
            return {
                "success": True,
                "tool_name": request.tool_name,
                "result": execution_result.get("result", {}),
                "execution_time": execution_result.get("executionTime"),
                "message": "Tool executed successfully"
            }

        # Get the connection for the required app
        connection_result = supabase.table('composio_connections').select("*").eq(
            "employee_id", employee["id"]
        ).eq("app_name", required_app).eq("status", "active").single().execute()

        if not connection_result.data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"No active {required_app} connection found. Please connect your {required_app.title()} account first."
            )

        connection = connection_result.data

        # Check if token needs refresh
        expires_at = datetime.fromisoformat(connection["expires_at"].replace('Z', '+00:00'))
        if expires_at <= datetime.now(timezone.utc) + timedelta(minutes=5):
            # Refresh token if needed
            await refresh_oauth_token(employee["id"], required_app, connection)
            # Re-fetch updated connection
            connection_result = supabase.table('composio_connections').select("*").eq(
                "employee_id", employee["id"]
            ).eq("app_name", required_app).eq("status", "active").single().execute()
            connection = connection_result.data

        # Decrypt access token
        access_token = decrypt_data(connection["access_token"])

        # Execute tool with our OAuth token via Composio
        entity_id = employee.get("composio_entity_id")
        if not entity_id:
            entity_id = await get_or_create_composio_entity(employee["id"], employee["email"])

        # Inject our OAuth token into the execution
        execution_data = {
            "user_id": entity_id,
            "tool_name": request.tool_name,
            "parameters": request.parameters,
            "auth_config": {
                "access_token": access_token,
                "token_type": "Bearer"
            }
        }

        response = await http_client.post(
            f"{COMPOSIO_BASE_URL}/v3/tools/execute",
            headers={"x-api-key": COMPOSIO_API_KEY, "Content-Type": "application/json"},
            json=execution_data
        )

        if response.status_code != 200:
            logger.error(f"Tool execution failed: {response.text}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Tool execution failed"
            )

        execution_result = response.json()

        # Return white-labeled response
        return {
            "success": True,
            "tool_name": request.tool_name,
            "result": execution_result.get("result", {}),
            "execution_time": execution_result.get("executionTime"),
            "message": "Tool executed successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Tool execution error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Tool execution failed"
        )

@app.post("/api/uru/composio/webhook")
async def handle_webhook(request: ComposioWebhookRequest):
    """Handle Composio webhooks (white-labeled)"""
    try:
        logger.info(f"Received webhook: {request.event_type} for entity {request.entity_id}")

        # Extract employee ID from entity ID
        if not request.entity_id.startswith("uru_employee_"):
            logger.warning(f"Unknown entity format: {request.entity_id}")
            return {"success": True, "message": "Webhook processed"}

        employee_id = request.entity_id.replace("uru_employee_", "")

        # Handle different webhook events
        if request.event_type == "connection.created":
            await handle_connection_created(employee_id, request)
        elif request.event_type == "connection.deleted":
            await handle_connection_deleted(employee_id, request)
        elif request.event_type == "connection.updated":
            await handle_connection_updated(employee_id, request)
        else:
            logger.info(f"Unhandled webhook event: {request.event_type}")

        return {"success": True, "message": "Webhook processed"}

    except Exception as e:
        logger.error(f"Webhook processing failed: {e}")
        # Don't raise HTTP exceptions for webhooks - just log and return success
        return {"success": False, "error": str(e)}

async def handle_connection_created(employee_id: str, webhook_data: ComposioWebhookRequest):
    """Handle connection created webhook"""
    try:
        # Update oauth token metadata to reflect webhook status
        provider_name = f"composio_{webhook_data.app_name}"

        # Get existing token record
        existing = supabase.table('oauth_tokens').select("metadata").eq(
            "employee_id", employee_id
        ).eq("provider", provider_name).execute()

        if existing.data:
            current_metadata = existing.data[0].get("metadata", {})
            updated_metadata = {
                **current_metadata,
                **webhook_data.data,
                "webhook_received_at": datetime.now(timezone.utc).isoformat(),
                "composio_connection_id": webhook_data.connection_id,
                "status": "active"
            }

            supabase.table('oauth_tokens').update({
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "metadata": updated_metadata
            }).eq("employee_id", employee_id).eq("provider", provider_name).execute()

        logger.info(f"Connection activated for employee {employee_id}: {webhook_data.app_name}")

    except Exception as e:
        logger.error(f"Failed to handle connection created: {e}")

async def handle_connection_deleted(employee_id: str, webhook_data: ComposioWebhookRequest):
    """Handle connection deleted webhook"""
    try:
        # Update connection status in database
        supabase.table('composio_connections').update({
            "status": "disconnected",
            "updated_at": datetime.now(timezone.utc).isoformat()
        }).eq("employee_id", employee_id).eq(
            "composio_connection_id", webhook_data.connection_id
        ).execute()

        logger.info(f"Connection disconnected for employee {employee_id}: {webhook_data.app_name}")

    except Exception as e:
        logger.error(f"Failed to handle connection deleted: {e}")

async def handle_connection_updated(employee_id: str, webhook_data: ComposioWebhookRequest):
    """Handle connection updated webhook"""
    try:
        # Update oauth token metadata to reflect webhook status
        provider_name = f"composio_{webhook_data.app_name}"

        # Get existing token record
        existing = supabase.table('oauth_tokens').select("metadata").eq(
            "employee_id", employee_id
        ).eq("provider", provider_name).execute()

        if existing.data:
            current_metadata = existing.data[0].get("metadata", {})
            updated_metadata = {
                **current_metadata,
                **webhook_data.data,
                "webhook_received_at": datetime.now(timezone.utc).isoformat(),
                "composio_connection_id": webhook_data.connection_id
            }

            supabase.table('oauth_tokens').update({
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "metadata": updated_metadata
            }).eq("employee_id", employee_id).eq("provider", provider_name).execute()

        logger.info(f"Connection updated for employee {employee_id}: {webhook_data.app_name}")

    except Exception as e:
        logger.error(f"Failed to handle connection updated: {e}")

@app.get("/api/uru/composio/connections")
async def list_connections(employee: Dict[str, Any] = Depends(get_current_employee)):
    """List employee's app connections (white-labeled)"""
    try:
        result = supabase.table('composio_connections').select("*").eq(
            "employee_id", employee["id"]
        ).execute()

        # Transform to white-labeled response
        connections = []
        for conn in result.data:
            connections.append({
                "app_name": conn["app_name"],
                "status": conn["status"],
                "connected_at": conn["created_at"],
                "last_updated": conn["updated_at"]
            })

        return {
            "success": True,
            "connections": connections,
            "total": len(connections)
        }

    except Exception as e:
        logger.error(f"Failed to list connections: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve connections"
        )

@app.delete("/api/uru/composio/connections/{app_name}")
async def disconnect_app(
    app_name: str,
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Disconnect app (fully white-labeled)"""
    try:
        # Get connection details
        result = supabase.table('composio_connections').select("*").eq(
            "employee_id", employee["id"]
        ).eq("app_name", app_name.lower()).single().execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No connection found for {app_name}"
            )

        connection = result.data

        # Revoke Google OAuth token if we have one
        if connection.get("access_token"):
            try:
                access_token = decrypt_data(connection["access_token"])
                revoke_response = await http_client.post(
                    f"https://oauth2.googleapis.com/revoke?token={access_token}",
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                if revoke_response.status_code == 200:
                    logger.info(f"Successfully revoked Google token for {app_name}")
                else:
                    logger.warning(f"Failed to revoke Google token: {revoke_response.text}")
            except Exception as e:
                logger.warning(f"Error revoking Google token: {e}")

        # Update local database to mark as disconnected
        supabase.table('composio_connections').update({
            "status": "disconnected",
            "access_token": None,
            "refresh_token": None,
            "expires_at": None,
            "updated_at": datetime.now(timezone.utc).isoformat()
        }).eq("id", connection["id"]).execute()

        logger.info(f"Successfully disconnected {app_name} for employee {employee['id']}")

        return {
            "success": True,
            "app_name": app_name,
            "message": f"{app_name.title()} disconnected successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to disconnect app: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disconnect app"
        )

# ============================================================================
# NEW MULTI-INTEGRATION ENDPOINTS
# ============================================================================

@app.get("/api/uru/integrations/available")
async def get_available_integrations(
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Get all available integrations organized by category"""
    try:
        integrations = integration_registry.get_all_integrations()
        categories = integration_registry.get_categories()

        # Organize integrations by category
        categorized_integrations = {}
        for category in categories:
            categorized_integrations[category] = []

        for app_name, config in integrations.items():
            integration_data = {
                "id": app_name,
                "name": config.display_name,
                "description": config.description,
                "category": config.category,
                "auth_type": config.auth_type.value,
                "tier": config.tier,
                "capabilities": config.capabilities,
                "logo_url": config.logo_url,
                "is_enabled": config.is_enabled,
                "documentation_url": config.documentation_url
            }
            categorized_integrations[config.category].append(integration_data)

        return {
            "success": True,
            "categories": categorized_integrations,
            "total_integrations": len(integrations)
        }

    except Exception as e:
        logger.error(f"Failed to get available integrations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve available integrations"
        )

@app.get("/api/uru/integrations/connections")
async def get_user_connections(
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Get user's current integration connections with status"""
    try:
        # Get all oauth tokens for this employee
        oauth_result = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).execute()

        connections = []
        for token_record in oauth_result.data:
            provider = token_record["provider"]

            # Extract app name from provider (remove composio_ prefix)
            if provider.startswith("composio_"):
                app_name = provider.replace("composio_", "")
                integration_config = integration_registry.get_integration(app_name)

                if integration_config:
                    # Check if token is expired
                    expires_at = token_record.get("expires_at")
                    is_expired = False
                    if expires_at:
                        try:
                            expiry_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                            is_expired = expiry_time < datetime.now(timezone.utc)
                        except:
                            is_expired = True

                    connection_data = {
                        "id": app_name,
                        "name": integration_config.display_name,
                        "category": integration_config.category,
                        "logo_url": integration_config.logo_url,
                        "is_connected": True,
                        "is_expired": is_expired,
                        "connected_at": token_record.get("updated_at"),
                        "scopes": token_record.get("scopes", []),
                        "metadata": token_record.get("metadata", {})
                    }
                    connections.append(connection_data)

        return {
            "success": True,
            "connections": connections,
            "total_connections": len(connections)
        }

    except Exception as e:
        logger.error(f"Failed to get user connections: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve integration connections"
        )

@app.post("/api/uru/integrations/connect/{integration_id}")
async def connect_integration(
    integration_id: str,
    request: ComposioConnectRequest,
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Connect to a specific integration using white-labeled OAuth"""
    try:
        # Get integration configuration
        integration_config = integration_registry.get_integration(integration_id)
        if not integration_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Integration '{integration_id}' not found"
            )

        if not integration_config.is_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Integration '{integration_id}' is not enabled"
            )

        # Check if already connected
        provider_name = f"composio_{integration_id}"
        existing_token = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).eq("provider", provider_name).execute()

        if existing_token.data:
            return {
                "success": True,
                "integration_id": integration_id,
                "already_connected": True,
                "message": f"Your {integration_config.display_name} account is already connected"
            }

        # Handle different auth types
        if integration_config.auth_type == AuthType.OAUTH2:
            return await _handle_oauth2_connection(integration_config, employee, request)
        elif integration_config.auth_type == AuthType.API_KEY:
            return await _handle_api_key_connection(integration_config, employee, request)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Auth type '{integration_config.auth_type.value}' not yet supported"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Integration connection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to connect integration"
        )

async def _handle_oauth2_connection(
    integration_config: IntegrationConfig,
    employee: Dict[str, Any],
    request: ComposioConnectRequest
) -> Dict[str, Any]:
    """Handle OAuth2 connection flow"""
    if not integration_config.oauth_config:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OAuth configuration missing for integration"
        )

    oauth_config = integration_config.oauth_config

    # Generate OAuth state for security
    oauth_state = secrets.token_urlsafe(32)

    # Store OAuth state in database
    oauth_record = {
        "employee_id": employee["id"],
        "provider": integration_config.composio_app_name,
        "state_token": oauth_state,
        "redirect_url": request.redirect_url or f"{FRONTEND_URL}/app/settings",
        "requested_scopes": oauth_config.scopes,
        "expires_at": (datetime.now(timezone.utc) + timedelta(minutes=10)).isoformat()
    }

    supabase.table('oauth_states').upsert(
        oauth_record,
        on_conflict="employee_id,provider"
    ).execute()

    # Build OAuth URL
    scopes = " ".join(oauth_config.scopes)
    oauth_params = {
        "client_id": os.getenv(oauth_config.client_id_env),
        "redirect_uri": f"{COMPOSIO_SERVICE_URL}{oauth_config.redirect_uri_path}/{integration_config.composio_app_name}",
        "scope": scopes,
        "response_type": "code",
        "state": oauth_state,
        "access_type": "offline",
        "prompt": "consent"
    }

    authorization_url = oauth_config.authorization_url + "?" + urllib.parse.urlencode(oauth_params)

    logger.info(f"Generated OAuth URL for {integration_config.composio_app_name} for employee {employee['id']}")

    return {
        "success": True,
        "integration_id": integration_config.composio_app_name,
        "authorization_url": authorization_url,
        "state": oauth_state,
        "message": f"Visit the authorization URL to connect your {integration_config.display_name} account"
    }

async def _handle_api_key_connection(
    integration_config: IntegrationConfig,
    employee: Dict[str, Any],
    request: ComposioConnectRequest
) -> Dict[str, Any]:
    """Handle API Key connection flow"""
    # API Key connections would be handled differently
    # For now, return not implemented
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="API Key authentication not yet implemented"
    )

@app.get("/api/uru/integrations/oauth/callback/{integration_id}")
async def integration_oauth_callback(
    integration_id: str,
    code: str,
    state: str,
    error: Optional[str] = None
):
    """Generic OAuth callback handler for all integrations"""
    try:
        if error:
            logger.error(f"OAuth error for {integration_id}: {error}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=oauth_denied&integration={integration_id}",
                status_code=302
            )

        # Get integration configuration
        integration_config = integration_registry.get_integration(integration_id)
        if not integration_config or not integration_config.oauth_config:
            logger.error(f"Invalid integration or missing OAuth config: {integration_id}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=invalid_integration&integration={integration_id}",
                status_code=302
            )

        # Verify OAuth state
        oauth_state_result = supabase.table('oauth_states').select("*").eq(
            "state_token", state
        ).eq("provider", integration_id).single().execute()

        if not oauth_state_result.data:
            logger.error(f"Invalid OAuth state: {state}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=invalid_state&integration={integration_id}",
                status_code=302
            )

        oauth_state_data = oauth_state_result.data
        employee_id = oauth_state_data["employee_id"]
        redirect_url = oauth_state_data["redirect_url"]
        oauth_config = integration_config.oauth_config

        # Exchange code for tokens
        token_data = {
            "client_id": os.getenv(oauth_config.client_id_env),
            "client_secret": os.getenv(oauth_config.client_secret_env),
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": f"{COMPOSIO_SERVICE_URL}{oauth_config.redirect_uri_path}/{integration_id}"
        }

        token_response = await http_client.post(
            oauth_config.token_url,
            data=token_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )

        if token_response.status_code != 200:
            logger.error(f"Token exchange failed for {integration_id}: {token_response.text}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=token_exchange_failed&integration={integration_id}",
                status_code=302
            )

        tokens = token_response.json()
        access_token = tokens.get("access_token")
        refresh_token = tokens.get("refresh_token")
        expires_in = tokens.get("expires_in", 3600)

        if not access_token:
            logger.error(f"No access token received for {integration_id}")
            return RedirectResponse(
                url=f"{FRONTEND_URL}/app/settings?error=no_access_token&integration={integration_id}",
                status_code=302
            )

        # Store tokens in oauth_tokens table
        provider_name = f"composio_{integration_id}"
        oauth_record = {
            "employee_id": employee_id,
            "provider": provider_name,
            "access_token": encrypt_data(access_token),
            "refresh_token": encrypt_data(refresh_token) if refresh_token else None,
            "expires_at": (datetime.now(timezone.utc) + timedelta(seconds=expires_in)).isoformat(),
            "scopes": oauth_config.scopes,
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "metadata": {
                "integration_id": integration_id,
                "integration_name": integration_config.display_name,
                "connected_via": "uru_oauth",
                "auth_type": integration_config.auth_type.value,
                "connected_at": datetime.now(timezone.utc).isoformat()
            }
        }

        # Upsert oauth token record
        supabase.table('oauth_tokens').upsert(
            oauth_record,
            on_conflict="employee_id,provider"
        ).execute()

        # Clean up OAuth state
        supabase.table('oauth_states').delete().eq("state_token", state).execute()

        logger.info(f"Successfully connected {integration_id} for employee {employee_id}")

        # Redirect back to frontend with success
        return RedirectResponse(
            url=f"{redirect_url}?success=true&integration={integration_id}&connected=true",
            status_code=302
        )

    except Exception as e:
        logger.error(f"OAuth callback error for {integration_id}: {e}")
        return RedirectResponse(
            url=f"{FRONTEND_URL}/app/settings?error=callback_failed&integration={integration_id}",
            status_code=302
        )

@app.delete("/api/uru/integrations/disconnect/{integration_id}")
async def disconnect_integration(
    integration_id: str,
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Disconnect an integration by removing stored tokens"""
    try:
        provider_name = f"composio_{integration_id}"

        # Delete OAuth tokens
        result = supabase.table('oauth_tokens').delete().eq(
            "employee_id", employee["id"]
        ).eq("provider", provider_name).execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Integration '{integration_id}' not connected"
            )

        logger.info(f"Disconnected {integration_id} for employee {employee['id']}")

        return {
            "success": True,
            "integration_id": integration_id,
            "message": f"Successfully disconnected {integration_id}"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to disconnect {integration_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disconnect integration"
        )

@app.post("/api/uru/integrations/execute")
async def execute_integration_tool(
    request: Dict[str, Any],
    employee: Dict[str, Any] = Depends(get_current_employee)
):
    """Execute a tool for a specific integration"""
    try:
        tool_name = request.get("tool_name")
        parameters = request.get("parameters", {})

        if not tool_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="tool_name is required"
            )

        logger.info(f"Executing integration tool: {tool_name} for employee {employee['id']}")
        logger.info(f"Parameters: {parameters}")

        # Extract integration ID from tool name (e.g., "slack_send_message" -> "slack")
        integration_id = tool_name.split('_')[0]

        # Check if user has this integration connected
        provider_name = f"composio_{integration_id}"
        oauth_result = supabase.table('oauth_tokens').select("*").eq(
            "employee_id", employee["id"]
        ).eq("provider", provider_name).execute()

        if not oauth_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Integration '{integration_id}' not connected"
            )

        oauth_record = oauth_result.data[0]

        # Check if token is expired
        expires_at = oauth_record.get("expires_at")
        if expires_at:
            try:
                expiry_time = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                if expiry_time < datetime.now(timezone.utc):
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail=f"Integration '{integration_id}' token expired"
                    )
            except:
                pass

        # Decrypt access token
        encrypted_token = oauth_record["access_token"]
        access_token = decrypt_data(encrypted_token)

        # Execute tool via Composio
        result = await _execute_composio_tool(integration_id, tool_name, parameters, access_token)

        logger.info(f"Tool execution completed: {tool_name}")

        return {
            "success": True,
            "result": result,
            "integration_id": integration_id,
            "tool_name": tool_name
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Integration tool execution failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute integration tool"
        )

async def _execute_composio_tool(integration_id: str, tool_name: str, parameters: Dict[str, Any], access_token: str):
    """Execute a tool via Composio API"""
    try:
        # Get integration configuration
        integration_config = integration_registry.get_integration(integration_id)
        if not integration_config:
            raise Exception(f"Integration '{integration_id}' not found in registry")

        # For now, return a mock response since we need to implement actual Composio API calls
        # In a real implementation, this would call the Composio API with the access token

        logger.info(f"Mock execution of {tool_name} for {integration_id}")

        # Mock responses based on tool type
        if "send_message" in tool_name:
            return {
                "message_id": f"msg_{secrets.token_hex(8)}",
                "status": "sent",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "channel": parameters.get("channel", "general"),
                "text": parameters.get("message", parameters.get("text", ""))
            }
        elif "list" in tool_name:
            return {
                "items": [
                    {
                        "id": f"item_{i}",
                        "name": f"Sample {tool_name.replace('_', ' ').title()} {i}",
                        "created_at": datetime.now(timezone.utc).isoformat()
                    }
                    for i in range(1, min(parameters.get("limit", 5) + 1, 11))
                ],
                "total": parameters.get("limit", 5)
            }
        elif "create" in tool_name:
            return {
                "id": f"created_{secrets.token_hex(8)}",
                "status": "created",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                **{k: v for k, v in parameters.items() if k not in ["limit", "max_results"]}
            }
        else:
            return {
                "status": "success",
                "tool": tool_name,
                "integration": integration_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "parameters": parameters
            }

    except Exception as e:
        logger.error(f"Composio tool execution failed: {e}")
        raise Exception(f"Tool execution failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
